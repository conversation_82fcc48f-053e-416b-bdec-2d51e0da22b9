#!/usr/bin/env python3
"""
Fix graph database dimension mismatch issue.

This script will:
1. Drop existing graph tables with wrong dimensions
2. Allow the system to recreate them with correct dimensions
"""

import asyncio
import asyncpg
import os
import sys
from typing import List

# Database connection settings
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'user': 'postgres',
    'password': 'postgres',
    'database': 'postgres'
}

SCHEMA_NAME = 'cscsrag'

# Graph tables that might have dimension issues
GRAPH_TABLES = [
    'graphs_entities',
    'graphs_relations', 
    'graphs_communities',
    'graphs_document_entities',
    'graphs_document_relations',
    'graphs_collection_entities',
    'graphs_collection_relations'
]

async def check_table_exists(conn: asyncpg.Connection, table_name: str) -> bool:
    """Check if a table exists in the schema."""
    result = await conn.fetchval("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = $1 AND table_name = $2
        )
    """, SCHEMA_NAME, table_name)
    return result

async def get_vector_dimension(conn: asyncpg.Connection, table_name: str, column_name: str) -> int:
    """Get the dimension of a vector column."""
    try:
        result = await conn.fetchval("""
            SELECT pg_catalog.format_type(a.atttypid, a.atttypmod) 
            FROM pg_attribute a 
            JOIN pg_class cl ON a.attrelid = cl.oid 
            JOIN pg_namespace n ON cl.relnamespace = n.oid
            WHERE n.nspname = $1 
            AND cl.relname = $2 
            AND a.attname = $3
        """, SCHEMA_NAME, table_name, column_name)
        
        if result and 'vector(' in result:
            # Extract dimension from vector(N) format
            dim_str = result.split('vector(')[1].split(')')[0]
            return int(dim_str)
        return 0
    except Exception as e:
        print(f"Error getting dimension for {table_name}.{column_name}: {e}")
        return 0

async def drop_table(conn: asyncpg.Connection, table_name: str) -> bool:
    """Drop a table if it exists."""
    try:
        await conn.execute(f'DROP TABLE IF EXISTS {SCHEMA_NAME}.{table_name} CASCADE')
        print(f"✓ Dropped table: {SCHEMA_NAME}.{table_name}")
        return True
    except Exception as e:
        print(f"✗ Error dropping table {table_name}: {e}")
        return False

async def main():
    """Main function to fix dimension issues."""
    print("🔧 Starting graph dimension fix...")
    print(f"📊 Target schema: {SCHEMA_NAME}")
    
    try:
        # Connect to database
        conn = await asyncpg.connect(**DB_CONFIG)
        print("✓ Connected to database")
        
        # Check each graph table
        tables_to_drop = []
        
        for table_name in GRAPH_TABLES:
            if await check_table_exists(conn, table_name):
                print(f"\n📋 Checking table: {table_name}")
                
                # Check for embedding columns
                embedding_columns = await conn.fetch("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_schema = $1 
                    AND table_name = $2 
                    AND column_name LIKE '%embedding%'
                """, SCHEMA_NAME, table_name)
                
                for row in embedding_columns:
                    column_name = row['column_name']
                    dimension = await get_vector_dimension(conn, table_name, column_name)
                    print(f"  📏 Column {column_name}: {dimension} dimensions")
                    
                    # If dimension is 1024 or unknown, mark for dropping
                    if dimension == 1024 or dimension == 0:
                        print(f"  ⚠️  Dimension mismatch detected (expected 512, got {dimension})")
                        tables_to_drop.append(table_name)
                        break
            else:
                print(f"📋 Table {table_name}: Not found (OK)")
        
        # Drop problematic tables
        if tables_to_drop:
            print(f"\n🗑️  Dropping {len(tables_to_drop)} tables with dimension issues...")
            
            for table_name in tables_to_drop:
                await drop_table(conn, table_name)
            
            print("\n✅ Tables dropped successfully!")
            print("🔄 The system will recreate these tables with correct dimensions on next startup.")
        else:
            print("\n✅ No dimension issues found!")
        
        await conn.close()
        print("✓ Database connection closed")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    print("=" * 60)
    print("🔧 R2R Graph Dimension Fix Tool")
    print("=" * 60)
    
    # Confirm action
    response = input("\n⚠️  This will drop existing graph tables. Continue? (y/N): ")
    if response.lower() != 'y':
        print("❌ Operation cancelled.")
        sys.exit(0)
    
    asyncio.run(main())
    
    print("\n" + "=" * 60)
    print("🎉 Fix completed! Please restart the R2R server.")
    print("=" * 60)
